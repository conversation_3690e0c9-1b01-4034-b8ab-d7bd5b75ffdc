{"version": "2.1.9", "results": [[":test/integration/end-to-end.test.ts", {"duration": 48.85012499999999, "failed": true}], [":test/core/translation-index.test.ts", {"duration": 20.453666999999996, "failed": true}], [":test/performance/benchmarks.test.ts", {"duration": 461.20691600000004, "failed": true}], [":test/server/mcp-server.test.ts", {"duration": 77.34174999999999, "failed": true}], [":test/core/file-watcher.test.ts", {"duration": 4352.940791999999, "failed": true}], [":test/integration/cli.test.ts", {"duration": 125.91312499999998, "failed": true}], [":test/utils/json-operations.test.ts", {"duration": 32.63770899999997, "failed": true}], [":test/utils/error-types.test.ts", {"duration": 14.833499999999987, "failed": true}], [":test/utils/path-parser.test.ts", {"duration": 15.019083999999992, "failed": false}], [":tests/basic-functionality.test.ts", {"duration": 0, "failed": true}]]}