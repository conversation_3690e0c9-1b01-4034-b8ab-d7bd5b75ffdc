/**
 * Command line argument parsing utilities
 */

import { ServerConfig } from '../types/translation.js';
import { resolve } from 'path';

/**
 * Parse command line arguments
 */
export function parseArgs(): Partial<ServerConfig> {
  const args = process.argv.slice(2);
  const config: Partial<ServerConfig> = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case '--dir':
      case '-d':
        if (nextArg && !nextArg.startsWith('-')) {
          config.translationDir = resolve(nextArg);
          i++;
        }
        break;
      
      case '--base-language':
      case '-b':
        if (nextArg && !nextArg.startsWith('-')) {
          config.baseLanguage = nextArg;
          i++;
        }
        break;
      
      case '--debug':
        config.debug = true;
        break;
      
      case '--name':
      case '-n':
        if (nextArg && !nextArg.startsWith('-')) {
          config.name = nextArg;
          i++;
        }
        break;
      
      case '--version':
      case '-v':
        if (nextArg && !nextArg.startsWith('-')) {
          config.version = nextArg;
          i++;
        }
        break;
      
      case '--help':
      case '-h':
        printHelp();
        process.exit(0);
        break;
      
      default:
        if (arg && arg.startsWith('-')) {
          console.error(`Unknown option: ${arg}`);
          printHelp();
          process.exit(1);
        }
        // Assume it's a directory path if no flag is provided
        if (arg && !config.translationDir) {
          config.translationDir = resolve(arg);
        }
        break;
    }
  }

  return config;
}

/**
 * Print help information
 */
export function printHelp(): void {
  console.log(`
i18n MCP Server - High-performance translation file management

Usage: i18n-mcp [options] [translation-directory]

Options:
  -d, --dir <path>           Translation files directory (default: ./locales)
  -b, --base-language <lang> Base language for structure template (default: en)
  -n, --name <name>          Server name (default: i18n-mcp)
  -v, --version <version>    Server version (default: 1.0.0)
  --debug                    Enable debug logging
  -h, --help                 Show this help message

Examples:
  i18n-mcp ./locales                    # Watch ./locales directory
  i18n-mcp --dir ./i18n --debug        # Watch ./i18n with debug logging
  i18n-mcp --base-language fr ./locales # Use French as base language

Environment Variables:
  I18N_MCP_DIR              Translation directory
  I18N_MCP_BASE_LANGUAGE    Base language
  I18N_MCP_DEBUG            Enable debug mode (true/false)
`);
}

/**
 * Load configuration from environment variables
 */
export function loadEnvConfig(): Partial<ServerConfig> {
  const config: Partial<ServerConfig> = {};

  if (process.env.I18N_MCP_DIR) {
    config.translationDir = resolve(process.env.I18N_MCP_DIR);
  }

  if (process.env.I18N_MCP_BASE_LANGUAGE) {
    config.baseLanguage = process.env.I18N_MCP_BASE_LANGUAGE;
  }

  if (process.env.I18N_MCP_DEBUG) {
    config.debug = process.env.I18N_MCP_DEBUG.toLowerCase() === 'true';
  }

  return config;
}
