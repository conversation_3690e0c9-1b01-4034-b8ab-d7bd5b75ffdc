/**
 * Smart file watcher with Chokidar v4 for translation files
 */

import chokidar, { FSWatcher } from 'chokidar';
import { promises as fs } from 'fs';
import { TranslationIndex } from './translation-index.js';
import { FileWatchEvent, FileWatchError } from '../types/translation.js';
import { debounce } from '../utils/path-parser.js';
import { FileProcessor } from './file-processor.js';
import { EventEmitter } from 'events';

/**
 * Configuration for the file watcher
 */
export interface FileWatcherConfig {
  /** Directory to watch for translation files */
  translationDir: string;
  /** Debounce delay in milliseconds */
  debounceMs?: number;
  /** File patterns to ignore */
  ignored?: string[];
  /** Enable debug logging */
  debug?: boolean;
}

/**
 * Translation file watcher with optimized change detection
 */
export class TranslationFileWatcher extends EventEmitter {
  private watcher?: FSWatcher;
  private readonly debouncedProcessChange: (path: string, eventType: 'add' | 'change') => void;
  private readonly config: Required<FileWatcherConfig>;
  private readonly processedFiles = new Set<string>();

  constructor(
    config: FileWatcherConfig,
    private readonly index: TranslationIndex
  ) {
    super();
    
    this.config = {
      debounceMs: 100,
      ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**'],
      debug: false,
      ...config
    };

    // Debounce file changes to handle rapid successive writes
    this.debouncedProcessChange = debounce(
      this.processFileChange.bind(this), 
      this.config.debounceMs
    );

    if (this.config.debug) {
      console.log(`📁 FileWatcher configured for: ${this.config.translationDir}`);
    }
  }

  /**
   * Start watching for file changes
   */
  async start(): Promise<void> {
    try {
      // Verify the translation directory exists
      await fs.access(this.config.translationDir);
    } catch (error) {
      throw new FileWatchError(
        `Translation directory does not exist: ${this.config.translationDir}`,
        { path: this.config.translationDir, error }
      );
    }

    this.watcher = chokidar.watch(this.config.translationDir, {
      // Optimized settings for performance
      persistent: true,
      ignoreInitial: false,
      followSymlinks: false,
      
      // Use native fsevents on macOS, fs.watch elsewhere
      usePolling: false,
      
      // Only watch JSON files
      ignored: [
        ...this.config.ignored,
        (path: string) => !path.endsWith('.json')
      ],
      
      // Performance optimizations
      alwaysStat: false,
      depth: 3, // Limit recursion depth
      
      // Reduce OS resource usage
      awaitWriteFinish: {
        stabilityThreshold: this.config.debounceMs,
        pollInterval: Math.max(50, this.config.debounceMs / 2)
      }
    });

    this.watcher
      .on('add', (path: string) => {
        if (this.config.debug) {
          console.log(`📄 File added: ${path}`);
        }
        this.debouncedProcessChange(path, 'add');
      })
      .on('change', (path: string) => {
        if (this.config.debug) {
          console.log(`📝 File changed: ${path}`);
        }
        this.debouncedProcessChange(path, 'change');
      })
      .on('unlink', (path: string) => {
        if (this.config.debug) {
          console.log(`🗑️ File deleted: ${path}`);
        }
        this.handleFileDelete(path);
      })
      .on('error', (error: unknown) => {
        console.error('📁 Watcher error:', error);
        this.emit('error', new FileWatchError('File watcher error', { error }));
      })
      .on('ready', () => {
        if (this.config.debug) {
          console.log(`📁 Initial scan complete. Watching: ${this.config.translationDir}`);
        }
        this.emit('ready');
      });

    if (this.config.debug) {
      console.log(`📁 Started watching translation files in: ${this.config.translationDir}`);
    }
  }

  /**
   * Stop watching for file changes
   */
  async stop(): Promise<void> {
    if (this.watcher) {
      await this.watcher.close();
      this.watcher = undefined;
      this.processedFiles.clear();
      
      if (this.config.debug) {
        console.log('📁 File watcher stopped');
      }
    }
  }

  /**
   * Get list of currently watched files
   */
  getWatchedFiles(): string[] {
    if (!this.watcher) {
      return [];
    }
    
    const watched = this.watcher.getWatched();
    const files: string[] = [];
    
    for (const [dir, filenames] of Object.entries(watched)) {
      if (Array.isArray(filenames)) {
        for (const filename of filenames) {
          if (filename.endsWith('.json')) {
            files.push(`${dir}/${filename}`);
          }
        }
      }
    }
    
    return files;
  }

  /**
   * Manually trigger processing of a file
   */
  async processFile(filePath: string): Promise<void> {
    await this.processFileChange(filePath, 'change');
  }

  /**
   * Process file changes and update the index
   */
  private async processFileChange(filePath: string, eventType: 'add' | 'change'): Promise<void> {
    try {
      // Skip if we've already processed this file recently
      if (this.processedFiles.has(filePath)) {
        return;
      }

      this.processedFiles.add(filePath);

      // Remove from processed set after a delay to allow for reprocessing
      setTimeout(() => {
        this.processedFiles.delete(filePath);
      }, this.config.debounceMs * 2);

      const language = FileProcessor.extractLanguageFromPath(filePath);

      await FileProcessor.processTranslationFile(filePath, language, this.index, this.config.debug);

      const event: FileWatchEvent = {
        type: eventType,
        path: filePath,
        language,
        timestamp: Date.now()
      };

      this.emit('fileProcessed', event);

    } catch (error) {
      const watchError = new FileWatchError(
        `Failed to process file ${filePath}`,
        { path: filePath, error }
      );

      console.error(`❌ ${watchError.message}:`, error);
      this.emit('error', watchError);
    }
  }

  /**
   * Handle file deletion
   */
  private handleFileDelete(filePath: string): void {
    try {
      const language = FileProcessor.extractLanguageFromPath(filePath);

      // Clear all translations from this file
      const keysToDelete: string[] = [];

      // Find all keys that belong to this file
      for (const keyPath of this.index.getKeys()) {
        const entry = this.index.get(keyPath, language);
        if (entry && typeof entry === 'object' && 'file' in entry && entry.file === filePath) {
          keysToDelete.push(keyPath);
        }
      }

      // Delete the keys
      for (const keyPath of keysToDelete) {
        this.index.delete(keyPath, language);
      }

      const event: FileWatchEvent = {
        type: 'unlink',
        path: filePath,
        language,
        timestamp: Date.now()
      };

      this.emit('fileProcessed', event);

      if (this.config.debug) {
        console.log(`🗑️ Removed ${language} translations from index: ${filePath}`);
      }

    } catch (error) {
      console.error(`Failed to handle file deletion ${filePath}:`, error);
    }
  }

  /**
   * Get watcher statistics
   */
  getStats(): {
    isWatching: boolean;
    watchedFiles: number;
    processedFiles: number;
  } {
    return {
      isWatching: !!this.watcher,
      watchedFiles: this.getWatchedFiles().length,
      processedFiles: this.processedFiles.size
    };
  }
}
