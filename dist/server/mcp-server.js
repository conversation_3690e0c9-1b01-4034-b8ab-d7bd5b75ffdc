/**
 * Main MCP server implementation for i18n translation management
 */
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
import { MCPTools } from './mcp-tools.js';
/**
 * High-level MCP server for translation management
 */
export class TranslationMCPServer {
    server;
    index;
    fileWatcher;
    config;
    constructor(config) {
        this.config = {
            baseLanguage: 'en',
            debug: false,
            watchOptions: {
                debounceMs: 100,
                ignored: ['**/node_modules/**', '**/.git/**']
            },
            ...config
        };
        // Initialize core components
        this.index = new TranslationIndex({
            baseLanguage: this.config.baseLanguage,
            debug: this.config.debug
        });
        this.fileWatcher = new TranslationFileWatcher({
            translationDir: this.config.translationDir,
            debounceMs: this.config.watchOptions.debounceMs,
            ignored: this.config.watchOptions.ignored,
            debug: this.config.debug
        }, this.index);
        // Initialize MCP server
        this.server = new McpServer({
            name: this.config.name,
            version: this.config.version,
        }, {
            capabilities: {
                tools: {},
                resources: {},
                prompts: {}
            }
        });
        this.setupTools();
        this.setupEventHandlers();
        if (this.config.debug) {
            console.log(`🚀 TranslationMCPServer initialized: ${this.config.name} v${this.config.version}`);
        }
    }
    /**
     * Setup MCP tools
     */
    setupTools() {
        const mcpTools = new MCPTools(this.index, this.fileWatcher, this.config);
        // Register each tool with the correct MCP SDK format
        mcpTools.registerTools(this.server);
        if (this.config.debug) {
            console.log('🔧 MCP tools registered successfully');
        }
    }
    /**
     * Setup event handlers for index and file watcher
     */
    setupEventHandlers() {
        // Index events
        this.index.on('set', (event) => {
            if (this.config.debug) {
                console.log(`📝 Translation set: ${event.keyPath} [${event.language}]`);
            }
        });
        this.index.on('delete', (event) => {
            if (this.config.debug) {
                console.log(`🗑️ Translation deleted: ${event.keyPath} [${event.language || 'all'}]`);
            }
        });
        // File watcher events
        this.fileWatcher.on('fileProcessed', (event) => {
            if (this.config.debug) {
                console.log(`📄 File processed: ${event.type} ${event.path} [${event.language}]`);
            }
        });
        this.fileWatcher.on('error', (error) => {
            console.error('📁 File watcher error:', error);
        });
        this.fileWatcher.on('ready', () => {
            if (this.config.debug) {
                console.log('📁 File watcher ready');
            }
        });
    }
    /**
     * Start the MCP server with STDIO transport
     */
    async start() {
        try {
            // Initialize file watcher first
            await this.fileWatcher.start();
            // Connect to STDIO transport
            const stdioTransport = new StdioServerTransport();
            await this.server.connect(stdioTransport);
            console.log(`🚀 Translation MCP Server started: ${this.config.name} v${this.config.version}`);
            console.log(`📁 Watching translations in: ${this.config.translationDir}`);
            console.log(`🌐 Base language: ${this.config.baseLanguage}`);
        }
        catch (error) {
            console.error('❌ Failed to start MCP server:', error);
            throw error;
        }
    }
    /**
     * Stop the MCP server
     */
    async stop() {
        try {
            await this.fileWatcher.stop();
            // Note: MCP SDK doesn't provide explicit server.stop() method
            console.log('🛑 Translation MCP Server stopped');
        }
        catch (error) {
            console.error('❌ Error stopping MCP server:', error);
            throw error;
        }
    }
    /**
     * Get the underlying index for advanced operations
     */
    getIndex() {
        return this.index;
    }
    /**
     * Get the file watcher for advanced operations
     */
    getFileWatcher() {
        return this.fileWatcher;
    }
}
//# sourceMappingURL=mcp-server.js.map