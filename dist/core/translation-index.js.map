{"version": 3, "file": "translation-index.js", "sourceRoot": "", "sources": ["../../src/core/translation-index.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAWL,UAAU,EACX,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAClE,OAAO,EAAE,wBAAwB,EAAE,MAAM,0BAA0B,CAAC;AACpE,OAAO,EAAE,0BAA0B,EAAE,MAAM,wBAAwB,CAAC;AAEpE;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAC/B,SAAS,GAAG,IAAI,GAAG,EAA8B,CAAC;IAClD,YAAY,GAAG,IAAI,GAAG,EAAuB,CAAC,CAAC,iBAAiB;IAChE,iBAAiB,GAAG,IAAI,GAAG,EAAe,CAAC;IAC3C,KAAK,CAAwB;IAE9C,4DAA4D;IACpD,UAAU,GAAa,EAAE,CAAC;IAC1B,SAAS,GAAG,KAAK,CAAC;IAET,MAAM,CAAmC;IAE1D,YAAY,MAA8B;QACxC,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,MAAM,GAAG;YACZ,YAAY,EAAE,KAAK;YACnB,KAAK,EAAE,KAAK;YACZ,GAAG,MAAM;YACT,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,IAAI;SAC1C,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAEpD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,uDAAuD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,OAAe,EAAE,QAAiB;QACpC,MAAM,QAAQ,GAAG,GAAG,OAAO,IAAI,QAAQ,IAAI,KAAK,EAAE,CAAC;QAEnD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAClD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,OAAe,EAAE,QAAgB,EAAE,KAAU,EAAE,QAAoC;QACrF,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,UAAU,CAAC,qBAAqB,OAAO,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,KAAK,GAAG,EAAwB,CAAC;YACjC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,MAAM,gBAAgB,GAAqB;YACzC,KAAK;YACL,IAAI,EAAE,QAAQ,EAAE,IAAI,IAAI,EAAE;YAC1B,IAAI,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC;YACzB,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;YAC7B,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;SACzB,CAAC;QAEF,KAAK,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC;QAEnC,0CAA0C;QAC1C,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAExC,yDAAyD;QACzD,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC1C,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAe,EAAE,QAAiB;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,2BAA2B;YAC3B,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAEvB,gDAAgD;gBAChD,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACpC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACxB,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,oCAAoC;YACpC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,KAAa,EAAE,UAAyB,EAAE,KAAK,EAAE,MAAM,EAAE;QACpE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,OAAO,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,OAAuB;QACvD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,OAAO,wBAAwB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QAEpC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,QAAQ;QAON,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,iBAAiB,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACjD,CAAC;QAED,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC9B,iBAAiB;YACjB,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAC1B,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;SAC7E,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAED,yBAAyB;IAEjB,gBAAgB;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAC3D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,OAAe,EAAE,KAAU;QACpD,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAEO,uBAAuB,CAAC,OAAe,EAAE,KAAU;QACzD,8DAA8D;QAC9D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC,CAAC;IACpD,CAAC;IAEO,eAAe,CAAC,OAAe;QACrC,+CAA+C;QAC/C,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAClD,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,OAAO,GAAG,MAAM,EAAE,CAAC;gBACxF,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAID;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,UAA4B;QAC5C,MAAM,MAAM,GAAG,MAAM,0BAA0B,CAAC,WAAW,CACzD,IAAI,CAAC,SAAS,EACd,UAAU,EACV,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EACvC,GAAG,EAAE;YACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC,EACD,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EAChE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CACtD,CAAC;QAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAAwD,EAAE;QAChF,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACtE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC;QAEzC,OAAO,0BAA0B,CAAC,iBAAiB,CACjD,IAAI,CAAC,SAAS,EACd,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EACpB,YAAY,EACZ,OAAO,EACP,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAC7C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,UAAgE,EAAE;QACnF,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC;QAC1D,OAAO,0BAA0B,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,eAAe,CAAC,CAAC;IACxG,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,OAAO,uBAAuB,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IACzE,CAAC;CACF"}