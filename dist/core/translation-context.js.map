{"version": 3, "file": "translation-context.js", "sourceRoot": "", "sources": ["../../src/core/translation-context.ts"], "names": [], "mappings": "AAAA;;GAEG;AAOH,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,MAAM,OAAO,wBAAwB;IACnC;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,UAAU,CACrB,SAA0C,EAC1C,UAAoB,EACpB,OAAe,EACf,OAAuB;QAEvB,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sBAAsB;QACtB,MAAM,aAAa,GAAuB,EAAE,CAAC;QAC7C,KAAK,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7D,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3D,aAAa,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;YACzC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAkB;YAC5B,OAAO;YACP,YAAY,EAAE,aAAa;YAC3B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,qBAAqB;QACrB,MAAM,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,UAAU,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC9C,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,mBAAmB,GAAuB,EAAE,CAAC;gBACnD,KAAK,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;oBACnE,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3D,mBAAmB,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;oBAC/C,CAAC;gBACH,CAAC;gBACD,MAAM,CAAC,MAAM,GAAG;oBACd,OAAO,EAAE,UAAU;oBACnB,YAAY,EAAE,mBAAmB;iBAClC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,KAAK,MAAM,YAAY,IAAI,UAAU,EAAE,CAAC;YACtC,qBAAqB;YACrB,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC;gBAChD,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAC/C,IAAI,UAAU,EAAE,CAAC;oBACf,MAAM,kBAAkB,GAAuB,EAAE,CAAC;oBAClD,KAAK,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;wBAClE,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4BAC3D,kBAAkB,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;wBAC9C,CAAC;oBACH,CAAC;oBACD,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;4BACnB,OAAO,EAAE,YAAY;4BACrB,YAAY,EAAE,kBAAkB;yBACjC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,qBAAqB;YACrB,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,CAAC,IAAI,YAAY,KAAK,OAAO,EAAE,CAAC;gBAC7F,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBACjD,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,oBAAoB,GAAuB,EAAE,CAAC;oBACpD,KAAK,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;wBACpE,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4BAC3D,oBAAoB,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;wBAChD,CAAC;oBACH,CAAC;oBACD,IAAI,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;4BACnB,OAAO,EAAE,YAAY;4BACrB,YAAY,EAAE,oBAAoB;yBACnC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF"}